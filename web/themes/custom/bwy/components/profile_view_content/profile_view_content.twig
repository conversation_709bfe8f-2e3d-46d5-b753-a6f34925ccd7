{#
/**
 * @file
 * Default theme implementation for profile component.
 *
 * Available variables:
 * - attributes: HTML attributes for the wrapper element.
 * - title_suffix: Additional output populated by modules.
 * - photo: User's profile photo.
 * - first_name: User's first name.
 * - last_name: User's last name.
 * - phone: User's phone number.
 * - linkedin: User's LinkedIn profile.
 * - education: User's education information.
 * - professional_field: User's professional field.
 * - professional_level: User's professional level.
 * - languages: Languages known by the user.
 * - residence_place: User's current residence place.
 * - interested_city: City of interest.
 * - return_reason: Reason for return.
 * - cv: User's CV.
 * - certificates: User's certificates.
 */
#}
{%
  set classes = [
  'profile',
  'clearfix',
]
%}

{% set section_classes = [
  'flex',
  'flex-col',
  'gap-6',
  'p-5',
  'bg-white',
  'border',
  'border-solid',
  'border-border-main',
  'rounded-lg',
] %}

<div{{ attributes.addClass(classes) }}>
  {{ title_suffix.contextual_links }}
  <div class="grid md:grid-cols-2 gap-6">
    <div class="{{ section_classes|join(' ') }}">
      <div class="flex flex-col lg:flex-row gap-6 lg:items-center">
        <div class="w-180px md:w-115px h-auto rounded-full overflow-hidden">
          {{ photo }}
        </div>
        <div>
          <div class="heading-3">
            <span>{{ first_name }}</span>
            <span>{{ last_name }}</span>
          </div>
          {{ email }}
          {{ phone }}
          {{ linkedin }}
        </div>
      </div>
      <div>
        <div class="heading-3">
          {{ 'Professional details'|t }}
        </div>
        <div class="flex flex-col gap-6">
          {{ education }}
          {{ professional_field }}
          {{ professional_level }}
          {{ languages }}
        </div>
      </div>
      <div>
        <div class="heading-3">
          {{ 'Location'|t }}
        </div>
        <div class="flex flex-col gap-6">
          {{ residence_place }}
          {{ interested_city }}
          {{ return_reason }}
        </div>
      </div>
    </div>
    <div class="{{ section_classes|join(' ') }}">
      <div>
        {{ cv }}
      </div>
      <div>
        {{ certificates }}
      </div>
    </div>
  </div>
</div>
