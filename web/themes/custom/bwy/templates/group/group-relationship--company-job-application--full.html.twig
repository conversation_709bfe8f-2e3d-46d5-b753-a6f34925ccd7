<h1>{{ 'Candidate'|t }}</h1>
<h2>{{ content.field_job_post }}</h2>
{# Current status #}
{{ content.field_status }}

{% include '@bwy/components/profile_view_content/profile_view_content.twig' with {
  attributes: attributes,
  photo: content.field_photo,
  first_name: content.field_first_name,
  last_name: content.field_last_name,
  email: content.field_email,
  phone: content.field_phone_number,
  linkedin: content.field_linkedin_profile,
  education: content.field_education,
  professional_field: content.field_professional_field,
  professional_level: content.field_professional_level,
  languages: content.field_languages,
  residence_place: content.field_residence_place,
  interested_city: content.field_interested_city,
  return_reason: content.field_return_reason
} %}

{# CV Documents Section #}
{% set cv_items = [] %}
{% for item in content.field_cv|merge(content.field_cv_new) %}
  {% set cv_items = cv_items|merge([{
    attributes: create_attribute(),
    content: item
  }]) %}
{% endfor %}

{% if cv_items %}
  {% include '@bwy/components/profile_documents/profile_documents.twig' with {
    attributes: create_attribute().addClass('cv-documents'),
    title_attributes: create_attribute(),
    label: 'CV'|t,
    items: cv_items
  } %}
{% endif %}

{# Certificates Documents Section #}
{% set certificate_items = [] %}
{% for item in content.field_certificates|merge(content.field_certificates_new) %}
  {% set certificate_items = certificate_items|merge([{
    attributes: create_attribute(),
    content: item
  }]) %}
{% endfor %}

{% if certificate_items %}
  {% include '@bwy/components/profile_documents/profile_documents.twig' with {
    attributes: create_attribute().addClass('certificate-documents'),
    title_attributes: create_attribute(),
    label: 'Certificates'|t,
    items: certificate_items
  } %}
{% endif %}

{{ content.field_message }}
{#
1. Change status with ajax - updatestatus callback. Permissions check!
2. Professional field? City? Languages? Professional level? LinkedIn? - да
3. Fix CV and Certificates - how to show them
#}
